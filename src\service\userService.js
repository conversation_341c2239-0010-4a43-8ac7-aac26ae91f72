const connection = require("../app/dataBase");
const { generateId } = require("../utils/snowflake");

class UserService {
  async create(user) {
    const { name, password } = user;

    const statement =
      "INSERT INTO `user` (id, name, password) VALUES (?, ?, ?)";

    const [result] = await connection.execute(statement, [
      generateId(),
      name,
      password,
    ]);

    return result;
  }

  async findUserByName(name) {
    const statement = "SELECT * FROM `user` WHERE name = ?";
    const [values] = await connection.execute(statement, [name]);
    return values;
  }
}

module.exports = new UserService();
