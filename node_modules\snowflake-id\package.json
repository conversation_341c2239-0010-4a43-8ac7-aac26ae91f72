{"name": "snowflake-id", "version": "1.1.0", "description": "Time based 64-bit unique id generator, inspired by Twitter snowflakes", "main": "lib/snowflake-id.js", "module": "src/snowflake-id.js", "devDependencies": {"babel-cli": "^6.26.0", "babel-loader": "^7.1.4", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.6.1", "rollup": "^0.60.7", "rollup-plugin-babel": "^3.0.4", "uglify-js": "^3.4.0"}, "repository": {"type": "git", "url": "git+https://github.com/thetetrabyte/snowflake-id.git"}, "keywords": ["snowflake", "flake", "unique", "id", "generator", "64-bit", "id", "generator"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/thetetrabyte/snowflake-id/issues"}, "homepage": "https://github.com/thetetrabyte/snowflake-id#readme", "dependencies": {"babel-runtime": "^6.26.0"}}