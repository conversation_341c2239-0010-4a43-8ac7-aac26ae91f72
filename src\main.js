// 1. 引入 Koa 框架
const Koa = require("koa");

// 2. 引入 Koa Router，用于处理路由
const KoaRouter = require("@koa/router");

// 3. 创建一个 Koa 实例（应用对象）
const app = new Koa();

// 4. 创建一个路由对象，所有路由都会以 /users 作为前缀
const userRouter = new KoaRouter({ prefix: "/users" });

// 5. 注册一个 GET 请求的路由：/users/list
// 当用户访问 /users/list 时，会返回 "user list"
userRouter.get("/list", (ctx, next) => {
  ctx.body = "user list"; // 设置响应体
});

// 6. 将 userRouter 的路由注册到应用中
app.use(userRouter.routes());

// 7. 允许 HTTP 方法校验，比如只允许 GET、POST 等路由中定义的方法，其他的会自动返回 405
app.use(userRouter.allowedMethods());

// 8. 启动服务器，监听 8000 端口
app.listen(8000, () => {
  console.log("coderhub服务器启动成功~"); // 启动成功后输出日志
});
