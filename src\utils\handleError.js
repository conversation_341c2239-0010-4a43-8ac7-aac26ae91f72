const app = require("../app/index");
const {
  NAME_IS_ALREADY_EXISTS,
  NAME_OR_PASSWORD_IS_REQUIRED,
} = require("../config/errorConstant");

app.on("error", (err, ctx) => {
  let code = 0;
  let message = "未知错误";

  switch (err) {
    case NAME_OR_PASSWORD_IS_REQUIRED:
      code = -1001;
      message = "用户名或密码不能为空";
      break;
    case NAME_IS_ALREADY_EXISTS:
      code = -1002;
      message = "用户名已存在";
      break;
  }

  ctx.body = { code, message };
});
