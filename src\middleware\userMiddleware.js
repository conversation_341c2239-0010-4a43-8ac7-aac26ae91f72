const userService = require("../service/userService");
const {
  NAME_IS_ALREADY_EXISTS,
  NAME_OR_PASSWORD_IS_REQUIRED,
} = require("../config/errorConstant");

const verifyUser = async (ctx, next) => {
  const { name, password } = ctx.request.body;
  if (!name || !password) {
    return ctx.app.emit("error", NAME_OR_PASSWORD_IS_REQUIRED, ctx);
  }

  const users = await userService.findUserByName(name);
  if (users.length > 0) {
    return ctx.app.emit("error", NAME_IS_ALREADY_EXISTS, ctx);
  }

  await next();
};

module.exports = verifyUser;
