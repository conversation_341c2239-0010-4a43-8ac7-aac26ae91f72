const mysql = require("mysql2");

// 创建连接池
const connectionPool = mysql.createPool({
  host: "localhost",
  port: 3306,
  database: "coderhub",
  user: "root",
  password: "Test@123456",
  connectionLimit: 5,
});

// 获取连接状态
connectionPool.getConnection((err, connection) => {
  if (err) {
    console.log("和数据库连接失败", err);
    return;
  }

  connection.connect((err) => {
    if (err) {
      console.log("和数据库交互失败", err);
    } else {
      console.log("数据库连接成功");
    }
  });
});

const connection = connectionPool.promise();

module.exports = connection;
