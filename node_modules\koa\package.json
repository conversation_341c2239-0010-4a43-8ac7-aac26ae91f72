{"name": "koa", "version": "3.0.0", "description": "Koa web app framework", "main": "lib/application.js", "exports": {".": {"require": "./lib/application.js", "import": "./dist/koa.mjs", "default": "./dist/koa.mjs"}, "./*": "./*.js", "./*.js": "./*.js", "./package": "./package.json", "./package.json": "./package.json"}, "scripts": {"test": "node --test", "test:coverage": "c8 --reporter=lcov --reporter=text-summary node --test", "lint": "standard", "lint:pretty": "standard | snazzy", "authors": "git log --format='%aN <%aE>' | sort -u > AUTHORS", "build": "gen-esm-wrapper . ./dist/koa.mjs", "prepare": "npm run build"}, "repository": "koajs/koa", "keywords": ["web", "app", "http", "application", "framework", "middleware", "rack"], "license": "MIT", "dependencies": {"accepts": "^1.3.5", "cache-content-type": "^1.0.0", "content-disposition": "~0.5.2", "content-type": "^1.0.4", "cookies": "~0.9.1", "debug": "^4.3.2", "delegates": "^1.0.0", "destroy": "^1.0.4", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "fresh": "~0.5.2", "http-assert": "^1.3.0", "http-errors": "^2.0.0", "koa-compose": "^4.1.0", "on-finished": "^2.3.0", "parseurl": "^1.3.2", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2"}, "devDependencies": {"c8": "^10.1.3", "gen-esm-wrapper": "^1.0.6", "snazzy": "^9.0.0", "standard": "^17.1.0", "supertest": "^7.0.0"}, "engines": {"node": ">= 18"}, "files": ["dist", "lib"], "homepage": "https://koajs.com"}