{"name": "biguint-format", "version": "1.0.2", "description": "An arbitrary length unsigned integer formatter library for Node.js", "main": "index.js", "scripts": {"test": "istanbul cover _mocha --report lcovonly"}, "repository": {"type": "git", "url": "https://github.com/T-PWK/biguint-format.git"}, "keywords": ["big", "bignum", "biguint", "uint", "integer", "hex", "hexadecimal", "octet", "decimal", "binary", "formatting"], "author": {"name": "<PERSON>", "url": "https://blog.abelotech.com/", "email": "<EMAIL>"}, "licenses": [{"type": "MIT"}], "bugs": {"url": "https://github.com/T-PWK/biguint-format/issues"}}